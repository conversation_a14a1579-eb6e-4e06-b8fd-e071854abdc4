"""
Credit Tracking System for Atlas

This module provides credit-based billing functionality while maintaining
backward compatibility with existing message-based subscription plans.

Credits are stored in the existing billing_subscriptions.metadata JSONB field:
{
    "credits_balance": 1500,
    "credits_allocated": 2000,
    "credits_used_this_period": 500,
    "last_credit_reset": "2025-01-01T00:00:00Z",
    "credit_system_enabled": true
}
"""

import json
from datetime import datetime, timezone
from typing import Dict, Any, Optional, Tuple
from utils.logger import logger
from utils.config import Configuration

config = Configuration()

# Credit allocations per subscription plan (monthly)
PLAN_CREDIT_ALLOCATIONS = {
    config.STRIPE_FREE_TIER_ID: {
        "monthly_credits": 500,  # ~10 simple tasks (50 credits each)
        "plan_name": "free",
        "message_equivalent": 10,  # Backward compatibility
    },
    config.STRIPE_PLUS_35_ID: {
        "monthly_credits": 3750,  # ~75 simple tasks (50 credits each)
        "plan_name": "plus",
        "message_equivalent": 75,  # Backward compatibility
    },
    config.STRIPE_PRO_75_ID: {
        "monthly_credits": 7500,  # ~150 simple tasks (50 credits each)
        "plan_name": "pro_75",
        "message_equivalent": 150,  # Backward compatibility
    },
    "admin": {
        "monthly_credits": 999999,  # Unlimited for admin
        "plan_name": "admin",
        "message_equivalent": 100000,
    },
}


class CreditTracker:
    """Manages credit allocation, tracking, and consumption for user accounts."""

    def __init__(self, supabase_client):
        """Initialize the credit tracker with a Supabase client."""
        self.client = supabase_client

    async def get_user_subscription_metadata(
        self, account_id: str
    ) -> Optional[Dict[str, Any]]:
        """Get the billing subscription metadata for a user."""
        try:
            # Get subscription from billing_subscriptions table
            result = (
                await self.client.schema("basejump")
                .from_("billing_subscriptions")
                .select("metadata, price_id, current_period_start, current_period_end")
                .eq("account_id", account_id)
                .execute()
            )

            if result.data:
                return result.data[0]
            return None

        except Exception as e:
            logger.error(
                f"Error getting subscription metadata for {account_id}: {str(e)}"
            )
            return None

    async def update_subscription_metadata(
        self, account_id: str, metadata: Dict[str, Any]
    ) -> bool:
        """Update the billing subscription metadata for a user."""
        try:
            result = (
                await self.client.schema("basejump")
                .from_("billing_subscriptions")
                .update({"metadata": metadata})
                .eq("account_id", account_id)
                .execute()
            )

            return len(result.data) > 0

        except Exception as e:
            logger.error(
                f"Error updating subscription metadata for {account_id}: {str(e)}"
            )
            return False

    async def initialize_credits_for_user(self, account_id: str, price_id: str) -> bool:
        """Initialize credit system for a user based on their subscription plan."""
        try:
            # Get plan allocation
            plan_config = PLAN_CREDIT_ALLOCATIONS.get(price_id)
            if not plan_config:
                logger.warning(f"No credit allocation found for price_id: {price_id}")
                return False

            # Get current subscription metadata
            subscription_data = await self.get_user_subscription_metadata(account_id)
            if not subscription_data:
                logger.error(f"No subscription found for account {account_id}")
                return False

            # Initialize or update credit metadata
            current_metadata = subscription_data.get("metadata", {})

            # Check if credits need to be reset (new billing period)
            now = datetime.now(timezone.utc)
            last_reset = current_metadata.get("last_credit_reset")
            current_period_start = subscription_data.get("current_period_start")

            should_reset = False
            if not last_reset or not current_period_start:
                should_reset = True
            else:
                # Parse dates for comparison
                try:
                    last_reset_date = datetime.fromisoformat(
                        last_reset.replace("Z", "+00:00")
                    )
                    period_start_date = datetime.fromisoformat(
                        current_period_start.replace("Z", "+00:00")
                    )
                    should_reset = period_start_date > last_reset_date
                except:
                    should_reset = True

            if should_reset:
                # Reset credits for new billing period
                current_metadata.update(
                    {
                        "credits_balance": plan_config["monthly_credits"],
                        "credits_allocated": plan_config["monthly_credits"],
                        "credits_used_this_period": 0,
                        "last_credit_reset": now.isoformat(),
                        "credit_system_enabled": True,
                        "plan_name": plan_config["plan_name"],
                    }
                )
                logger.info(
                    f"Reset credits for {account_id}: {plan_config['monthly_credits']} credits"
                )
            else:
                # Just ensure credit system is enabled
                current_metadata.update(
                    {
                        "credit_system_enabled": True,
                        "plan_name": plan_config["plan_name"],
                    }
                )
                # Initialize credits if not present
                if "credits_balance" not in current_metadata:
                    current_metadata.update(
                        {
                            "credits_balance": plan_config["monthly_credits"],
                            "credits_allocated": plan_config["monthly_credits"],
                            "credits_used_this_period": 0,
                            "last_credit_reset": now.isoformat(),
                        }
                    )

            # Update the subscription metadata
            return await self.update_subscription_metadata(account_id, current_metadata)

        except Exception as e:
            logger.error(f"Error initializing credits for {account_id}: {str(e)}")
            return False

    async def get_credit_balance(self, account_id: str) -> Dict[str, Any]:
        """Get current credit balance and usage information for a user."""
        try:
            subscription_data = await self.get_user_subscription_metadata(account_id)
            if not subscription_data:
                return {
                    "credits_balance": 0,
                    "credits_allocated": 0,
                    "credits_used_this_period": 0,
                    "credit_system_enabled": False,
                    "plan_name": "free",
                }

            metadata = subscription_data.get("metadata", {})
            price_id = subscription_data.get("price_id")

            # Initialize credits if not present
            if not metadata.get("credit_system_enabled"):
                await self.initialize_credits_for_user(account_id, price_id)
                # Re-fetch after initialization
                subscription_data = await self.get_user_subscription_metadata(
                    account_id
                )
                metadata = (
                    subscription_data.get("metadata", {}) if subscription_data else {}
                )

            return {
                "credits_balance": metadata.get("credits_balance", 0),
                "credits_allocated": metadata.get("credits_allocated", 0),
                "credits_used_this_period": metadata.get("credits_used_this_period", 0),
                "credit_system_enabled": metadata.get("credit_system_enabled", False),
                "plan_name": metadata.get("plan_name", "free"),
                "last_credit_reset": metadata.get("last_credit_reset"),
            }

        except Exception as e:
            logger.error(f"Error getting credit balance for {account_id}: {str(e)}")
            return {
                "credits_balance": 0,
                "credits_allocated": 0,
                "credits_used_this_period": 0,
                "credit_system_enabled": False,
                "plan_name": "free",
            }

    async def can_consume_credits(
        self, account_id: str, credits_needed: int
    ) -> Tuple[bool, str, Dict[str, Any]]:
        """Check if user has enough credits for an operation."""
        try:
            credit_info = await self.get_credit_balance(account_id)

            if not credit_info["credit_system_enabled"]:
                # Fall back to message-based system
                return (
                    True,
                    "Credit system not enabled, using message-based billing",
                    credit_info,
                )

            current_balance = credit_info["credits_balance"]

            if current_balance >= credits_needed:
                return True, "Sufficient credits available", credit_info
            else:
                return (
                    False,
                    f"Insufficient credits. Need {credits_needed}, have {current_balance}",
                    credit_info,
                )

        except Exception as e:
            logger.error(
                f"Error checking credit availability for {account_id}: {str(e)}"
            )
            return False, f"Error checking credits: {str(e)}", {}

    async def consume_credits(
        self, account_id: str, credits_used: int, description: str = "Agent run"
    ) -> bool:
        """Consume credits from user's balance."""
        try:
            subscription_data = await self.get_user_subscription_metadata(account_id)
            if not subscription_data:
                logger.error(f"No subscription found for account {account_id}")
                return False

            metadata = subscription_data.get("metadata", {})

            if not metadata.get("credit_system_enabled"):
                logger.info(
                    f"Credit system not enabled for {account_id}, skipping credit consumption"
                )
                return True

            # Update credit balance
            current_balance = metadata.get("credits_balance", 0)
            current_used = metadata.get("credits_used_this_period", 0)

            new_balance = max(0, current_balance - credits_used)
            new_used = current_used + credits_used

            metadata.update(
                {"credits_balance": new_balance, "credits_used_this_period": new_used}
            )

            # Update subscription metadata
            success = await self.update_subscription_metadata(account_id, metadata)

            if success:
                logger.info(
                    f"Consumed {credits_used} credits for {account_id}. New balance: {new_balance}"
                )

            return success

        except Exception as e:
            logger.error(f"Error consuming credits for {account_id}: {str(e)}")
            return False
